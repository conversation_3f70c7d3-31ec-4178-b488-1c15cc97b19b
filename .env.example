# Server Configuration
PORT=3001
NODE_ENV=development

# MongoDB Connection
MONGODB_URI=mongodb://localhost:27017/shortlet360

# JWT Configuration
JWT_SECRET=your_jwt_secret_key
JWT_LIFETIME=30d

# Email Configuration
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password

# Frontend URL
FRONTEND_URL=http://localhost:5173

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# Paystack Configuration
PAYSTACK_SECRET_KEY=your_paystack_secret_key
PAYSTACK_PUBLIC_KEY=your_paystack_public_key

# Prembly API Configuration (Legacy)
PREMBLY_API_KEY=your_prembly_api_key
PREMBLY_APP_ID=your_prembly_app_id

# YouVerify API Configuration
YOUVERIFY_API_TOKEN=your_youverify_api_token
YOUVERIFY_BASE_URL=https://api.youverify.co/v2/api
