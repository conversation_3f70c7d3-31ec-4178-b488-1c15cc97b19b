import { useState } from "react";
import { FiSend, FiTool } from "react-icons/fi";
import toast from "react-hot-toast";
import { formService } from "../../../services/api";

export default function ServiceRequest() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    service: "",
    description: "",
    address: {
      street: "",
      state: "Lagos",
      localGovernment: "",
      area: "",
    },
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const serviceOptions = [
    "Cleaning",
    "Plumbing",
    "Electrical",
    "Carpentry",
    "Painting",
    "Gardening",
    "Appliance Repair",
    "HVAC",
    "Locksmith",
    "Pest Control",
    "Furniture Assembly",
    "Moving Services",
    "Interior Design",
    "Home Security",
    "Other",
  ];

  // Lagos State Location Data (abbreviated for brevity)
  const lagosLocationData = {
    AGEGE: ["Atobaje", "Darocha", "Iloro/Onipetesi", "<PERSON>le Odo/Ayige"],
    "AJEROMI/IFELODUN": ["Aiyetoro", "Alaba", "Alakoto/Ibafon", "Amukoko"],
    ALIMISHO: ["Abesan", "Abaranje/Okerube", "Aboru", "Abule-Egba"],
    "AMUWO ODOFIN": ["Abule Osun Onireke", "Ado Soba", "Agboju", "Eko Akete"],
    APAPA: ["Abete", "Alafia", "Anjorin", "Apapa"],
    EPE: ["Agbowa", "Agbowa 2/Ado", "Aiyetoro/Lagbade/Agbala/Oloto"],
    "ETI-OSA": ["1004 Aboyade", "Addo/Okeira", "Ajah", "Ajiran/Osapa"],
    "IBEJU-LEKKI": ["Abraham Adesanya", "Ibeju", "Ise/Igbogun", "Iwerekun"],
    "IFAKO IJAIYE": ["Abule-Egba", "Agbado Dalemo", "Alagbado/Kollinton"],
    KOSEOFE: ["Agboyi", "Agidi/Orishigun", "Agiliti/Maidan", "Araromi/Ifako"],
    "LAGOS ISLAND": ["Ajele", "Alagba/Obadina", "Anikantamo", "Araromi Odo"],
    "LAGOS MAINLAND": ["Abule Ijesha", "Abule Oja", "Adekunle/Aiyetoro"],
    MUSHIN: ["Alafia/Adeoyo", "Alakara", "Babalosa", "Idi-Araba"],
    OJO: ["Ajangbadi", "Egan", "Etegbin", "Iba"],
    "OSHODI ISOLO": ["Afariogun", "Aigbaka", "Ailegun", "Ajao Estate"],
    SHOMOLU: ["Aiyetoro/Mafowoku", "Akoka/Anu-Oluwapo", "Apelehin"],
    SURULERE: ["Adeniran Ogunsanya", "Ogunbiade", "Airways", "Akinhanmi Cole"],
  };

  const handleChange = (e) => {
    const { name, value } = e.target;

    if (name.startsWith("address.")) {
      const addressField = name.split(".")[1];
      setFormData((prev) => ({
        ...prev,
        address: {
          ...prev.address,
          [addressField]: value,
          // Reset dependent fields when parent changes
          ...(addressField === "localGovernment" && { area: "" }),
        },
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      await formService.submitHomeServiceForm(formData);
      toast.success("Your service request has been submitted successfully!");
      setFormData({
        name: "",
        email: "",
        phone: "",
        service: "",
        description: "",
        address: {
          street: "",
          state: "Lagos",
          localGovernment: "",
          area: "",
        },
      });
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("Failed to submit your request. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-4 md:p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3 mb-6">
        <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
          <FiTool className="w-5 h-5 text-orange-600" />
        </div>
        <div>
          <h1 className="text-2xl font-semibold text-gray-800">HomeFix Service Request</h1>
          <p className="text-gray-600">Request professional home services from vetted artisans</p>
        </div>
      </div>

      {/* Service Request Form */}
      <div className="bg-white rounded-xl shadow p-6">
        <h2 className="text-xl font-semibold mb-6">Request a Service</h2>
        <p className="text-gray-600 mb-8">
          Fill out the form below to request a home service. Our team of
          professionals will get back to you shortly.
        </p>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label
                htmlFor="name"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Full Name *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                placeholder="Your full name"
              />
            </div>
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Email Address *
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                placeholder="Your email address"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label
                htmlFor="phone"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Phone Number *
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                required
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                placeholder="Your phone number"
              />
            </div>
            <div>
              <label
                htmlFor="service"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Service Type *
              </label>
              <select
                id="service"
                name="service"
                value={formData.service}
                onChange={handleChange}
                required
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">Select a service</option>
                {serviceOptions.map((service) => (
                  <option key={service} value={service}>
                    {service}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Address Section */}
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
              Service Address
            </h3>

            <div>
              <label
                htmlFor="street"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Street Number and Name *
              </label>
              <input
                type="text"
                id="street"
                name="address.street"
                value={formData.address.street}
                onChange={handleChange}
                required
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                placeholder="e.g., 123 Main Street"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label
                  htmlFor="state"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  State *
                </label>
                <select
                  id="state"
                  name="address.state"
                  value={formData.address.state}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="Lagos">Lagos</option>
                </select>
                <p className="text-xs text-gray-500 mt-1">
                  Currently available in Lagos only
                </p>
              </div>

              <div>
                <label
                  htmlFor="localGovernment"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Local Government *
                </label>
                <select
                  id="localGovernment"
                  name="address.localGovernment"
                  value={formData.address.localGovernment}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">Select LGA</option>
                  {Object.keys(lagosLocationData).map((lga) => (
                    <option key={lga} value={lga}>
                      {lga}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label
                  htmlFor="area"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Area *
                </label>
                <select
                  id="area"
                  name="address.area"
                  value={formData.address.area}
                  onChange={handleChange}
                  required
                  disabled={!formData.address.localGovernment}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
                >
                  <option value="">Select Area</option>
                  {formData.address.localGovernment &&
                    lagosLocationData[
                      formData.address.localGovernment
                    ]?.map((area) => (
                      <option key={area} value={area}>
                        {area}
                      </option>
                    ))}
                </select>
                {!formData.address.localGovernment && (
                  <p className="text-xs text-gray-500 mt-1">
                    Please select a Local Government first
                  </p>
                )}
              </div>
            </div>
          </div>

          <div>
            <label
              htmlFor="description"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Service Description *
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              required
              rows={5}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
              placeholder="Please describe what you need help with..."
            ></textarea>
          </div>

          <div className="flex justify-center">
            <button
              type="submit"
              disabled={isSubmitting}
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                "Submitting..."
              ) : (
                <>
                  <FiSend className="mr-2" /> Submit Request
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
